using UnityEngine;

/// <summary>
/// Example script showing how to properly set up a rope with player physics interaction.
/// This script demonstrates the correct way to configure the rope component.
/// </summary>
public class RopeSetupExample : MonoBehaviour {
    
    [<PERSON><PERSON>("Player Interaction Settings")]
    [Tooltip("Enable/disable player interaction")]
    public bool enablePlayerInteraction = true;
    
    [Tooltip("Force multiplier for player interactions")]
    [Range(0.1f, 10.0f)]
    public float forceMultiplier = 2.0f;
    
    [Tooltip("Radius around rope particles to check for players")]
    [Range(0.1f, 2.0f)]
    public float interactionRadius = 0.5f;
    
    [Tooltip("Check every N particles for performance")]
    [Range(1, 10)]
    public int checkStride = 2;
    
    [Header("Physics Settings")]
    [Tooltip("Rope stiffness")]
    [Range(0.01f, 1.0f)]
    public float stiffness = 0.8f;
    
    [Tooltip("Energy loss per frame")]
    [Range(0.0f, 1.0f)]
    public float energyLoss = 0.01f;
    
    [<PERSON>lt<PERSON>("Gravity multiplier")]
    [Range(0.0f, 1.0f)]
    public float gravityMultiplier = 1.0f;
    
    [Tooltip("Simulation substeps")]
    [Range(1, 10)]
    public int substeps = 4;
    
    [Tooltip("Solver iterations")]
    [Range(1, 32)]
    public int solverIterations = 3;
    
    void Start() {
        SetupRope();
    }
    
    /// <summary>
    /// Sets up the rope component with player interaction.
    /// This method shows the correct way to configure a rope for player physics interaction.
    /// </summary>
    void SetupRope() {
        // Try to find a rope component in the scene
        // Note: In a real project, you would get the rope component directly
        var ropeComponents = FindObjectsOfType<Component>();
        Component ropeComponent = null;
        
        foreach (var component in ropeComponents) {
            if (component.GetType().Name == "Rope") {
                ropeComponent = component;
                break;
            }
        }
        
        if (ropeComponent == null) {
            Debug.LogWarning("RopeSetupExample: No Rope component found in scene!");
            Debug.Log("To use this example:");
            Debug.Log("1. Add a Rope component from RopeToolkit to a GameObject");
            Debug.Log("2. Configure the rope spawn points");
            Debug.Log("3. Run this script to see the configuration example");
            return;
        }
        
        Debug.Log("Found Rope component: " + ropeComponent.name);
        Debug.Log("Configuring rope for player interaction...");
        
        // Example configuration code that you would use in your own scripts:
        LogConfigurationExample();
    }
    
    /// <summary>
    /// Logs the example configuration code that developers should use.
    /// </summary>
    void LogConfigurationExample() {
        Debug.Log("\n=== ROPE PLAYER INTERACTION SETUP EXAMPLE ===");
        Debug.Log("Copy this code to configure your rope:");
        Debug.Log("");
        Debug.Log("// Get the rope component");
        Debug.Log("var rope = GetComponent<RopeToolkit.Rope>();");
        Debug.Log("");
        Debug.Log("// Enable simulation and collisions");
        Debug.Log("rope.simulation.enabled = true;");
        Debug.Log("rope.collisions.enabled = true;");
        Debug.Log("rope.collisions.influenceRigidbodies = true;");
        Debug.Log("");
        Debug.Log("// Configure player interaction");
        Debug.Log($"rope.playerInteraction.enabled = {enablePlayerInteraction.ToString().ToLower()};");
        Debug.Log($"rope.playerInteraction.forceMultiplier = {forceMultiplier}f;");
        Debug.Log($"rope.playerInteraction.interactionRadius = {interactionRadius}f;");
        Debug.Log($"rope.playerInteraction.checkStride = {checkStride};");
        Debug.Log("");
        Debug.Log("// Optional: Fine-tune physics settings");
        Debug.Log($"rope.simulation.stiffness = {stiffness}f;");
        Debug.Log($"rope.simulation.energyLoss = {energyLoss}f;");
        Debug.Log($"rope.simulation.gravityMultiplier = {gravityMultiplier}f;");
        Debug.Log($"rope.simulation.substeps = {substeps};");
        Debug.Log($"rope.simulation.solverIterations = {solverIterations};");
        Debug.Log("");
        Debug.Log("=== END EXAMPLE ===");
    }
    
    /// <summary>
    /// Updates settings at runtime for testing purposes.
    /// </summary>
    void Update() {
        // This is just for demonstration - in a real project you would
        // configure the rope once in Start() or when needed
        if (Input.GetKeyDown(KeyCode.R)) {
            LogConfigurationExample();
        }
    }
    
    void OnGUI() {
        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label("Rope Player Interaction Example");
        GUILayout.Label("Press 'R' to show configuration code");
        GUILayout.Label("");
        GUILayout.Label("Current Settings:");
        GUILayout.Label($"Force Multiplier: {forceMultiplier}");
        GUILayout.Label($"Interaction Radius: {interactionRadius}");
        GUILayout.Label($"Check Stride: {checkStride}");
        GUILayout.EndArea();
    }
}
