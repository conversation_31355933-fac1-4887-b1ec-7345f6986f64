# Веревка убивает игроков при касании

## Что сделано

Добавлен простой код в `Assets\Plugins\Rope\Rope.cs`, который автоматически убивает игроков при касании веревки.

## Изменения

### 1. Включены коллизии по умолчанию
```csharp
public CollisionSettings collisions = new CollisionSettings() {
    enabled = true,  // Было false
    // ... остальные настройки
};
```

### 2. Добавлена проверка игроков в UpdateCollisionPlanes()
```csharp
// Check if collider belongs to a player and kill them
PlayerController player = collider.GetComponentInParent<PlayerController>();
if (player != null && player.health.IsAlive) {
    PlayerDeath playerDeath = player.GetComponent<PlayerDeath>();
    if (playerDeath != null && player.Object.HasStateAuthority) {
        playerDeath.DieFromGrinder(PlayerRef.None);
    }
}
```

## Как работает

1. **Веревка автоматически обнаруживает коллизии** с объектами в сцене
2. **При каждой коллизии проверяется**, есть ли у объекта PlayerController
3. **Если найден живой игрок**, вызывается `DieFromGrinder(PlayerRef.None)`
4. **Смерть происходит только на сервере** (HasStateAuthority)

## Использование

Просто добавьте компонент Rope к GameObject - больше ничего не нужно!

```csharp
// Веревка автоматически убивает игроков при касании
var rope = GetComponent<RopeToolkit.Rope>();
// Коллизии уже включены по умолчанию
// Код убийства игроков уже встроен
```

## Преимущества

✅ **Работает из коробки** - никаких дополнительных компонентов  
✅ **Минимум кода** - всего несколько строк  
✅ **Использует существующую систему** коллизий веревки  
✅ **Сетевая совместимость** - работает только на сервере  
✅ **Безопасность** - проверяет живых игроков  

## Требования

- Unity 2021.3+
- Fusion 2.0
- PlayerController с компонентом PlayerDeath
- Метод DieFromGrinder в PlayerDeath.cs

## Отладка

- Убедитесь, что у веревки включены коллизии (`collisions.enabled = true`)
- Проверьте, что игроки имеют коллайдеры
- Убедитесь, что PlayerDeath компонент присутствует на игроках

Теперь любая веревка в игре будет автоматически убивать игроков при касании!
