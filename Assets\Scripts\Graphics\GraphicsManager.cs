using UnityEngine;
using UnityEngine.Rendering.Universal;

namespace SimpleFPS {
    /// <summary>
    /// Enhanced graphics manager that handles dynamic render scale and integrates with existing settings
    /// </summary>
    public class GraphicsManager : MonoBehaviour {
        [Header("Render Scale Configuration")]
        [SerializeField] private bool enableDynamicRenderScale = true;
        [SerializeField]
        private RenderScaleProfile[] renderScaleProfiles = new RenderScaleProfile[] {
            new RenderScaleProfile { resolution = new Vector2Int(1920, 1080), renderScale = 0.4f, name = "HD" },
            new RenderScaleProfile { resolution = new Vector2Int(2560, 1440), renderScale = 0.3f, name = "QHD" },
            new RenderScaleProfile { resolution = new Vector2Int(3840, 2160), renderScale = 0.2f, name = "4K" }
        };

        [Header("Fallback Settings")]
        [SerializeField] private float minRenderScale = 0.1f;
        [SerializeField] private float maxRenderScale = 1.0f;
        [SerializeField] private bool useProportionalScaling = true;

        [Header("Debug")]
        [SerializeField] private bool showDebugInfo = true;

        private UniversalRenderPipelineAsset urpAsset;
        private Vector2Int lastResolution;
        private static GraphicsManager instance;

        public static GraphicsManager Instance => instance;

        void Awake() {
            if (instance == null) {
                instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else {
                Destroy(gameObject);
                return;
            }
        }

        void Start() {
            urpAsset = UniversalRenderPipeline.asset;
            if (urpAsset == null) {
                Debug.LogError("GraphicsManager: No URP asset found!");
                enabled = false;
                return;
            }

            ApplyGraphicsSettings();
            lastResolution = new Vector2Int(Screen.width, Screen.height);
        }

        void Update() {
            if (!enableDynamicRenderScale) return;

            Vector2Int currentResolution = new Vector2Int(Screen.width, Screen.height);
            if (currentResolution != lastResolution) {
                ApplyRenderScale(currentResolution);
                lastResolution = currentResolution;
            }
        }

        public void ApplyGraphicsSettings() {
            if (enableDynamicRenderScale) {
                Vector2Int currentResolution = new Vector2Int(Screen.width, Screen.height);
                ApplyRenderScale(currentResolution);
            }
        }

        void ApplyRenderScale(Vector2Int resolution) {
            if (urpAsset == null) return;

            float newRenderScale = CalculateOptimalRenderScale(resolution);
            urpAsset.renderScale = newRenderScale;

            if (showDebugInfo) {
                string profileName = GetProfileName(resolution);
                Debug.Log($"GraphicsManager: Resolution {resolution.x}x{resolution.y} ({profileName}), Render Scale: {newRenderScale:F3}");
            }
        }

        float CalculateOptimalRenderScale(Vector2Int resolution) {
            // Check for exact matches first
            foreach (var profile in renderScaleProfiles) {
                if (profile.resolution == resolution) {
                    return profile.renderScale;
                }
            }

            if (!useProportionalScaling) {
                // Use closest profile
                return GetClosestProfile(resolution).renderScale;
            }

            // Calculate proportional scaling based on pixel density
            int totalPixels = resolution.x * resolution.y;

            // Find the two closest profiles to interpolate between
            RenderScaleProfile lowerProfile = null;
            RenderScaleProfile upperProfile = null;

            foreach (var profile in renderScaleProfiles) {
                int profilePixels = profile.resolution.x * profile.resolution.y;

                if (profilePixels <= totalPixels) {
                    if (lowerProfile == null || profilePixels > (lowerProfile.resolution.x * lowerProfile.resolution.y)) {
                        lowerProfile = profile;
                    }
                }

                if (profilePixels >= totalPixels) {
                    if (upperProfile == null || profilePixels < (upperProfile.resolution.x * upperProfile.resolution.y)) {
                        upperProfile = profile;
                    }
                }
            }

            // Interpolate between profiles
            if (lowerProfile != null && upperProfile != null && lowerProfile != upperProfile) {
                int lowerPixels = lowerProfile.resolution.x * lowerProfile.resolution.y;
                int upperPixels = upperProfile.resolution.x * upperProfile.resolution.y;

                float ratio = (float)(totalPixels - lowerPixels) / (upperPixels - lowerPixels);
                float interpolatedScale = Mathf.Lerp(lowerProfile.renderScale, upperProfile.renderScale, ratio);

                return Mathf.Clamp(interpolatedScale, minRenderScale, maxRenderScale);
            }

            // Fallback to closest profile
            var closestProfile = GetClosestProfile(resolution);
            return closestProfile != null ? closestProfile.renderScale : 0.5f;
        }

        RenderScaleProfile GetClosestProfile(Vector2Int resolution) {
            if (renderScaleProfiles.Length == 0) return null;

            RenderScaleProfile closest = renderScaleProfiles[0];
            int targetPixels = resolution.x * resolution.y;
            int closestDiff = Mathf.Abs(targetPixels - (closest.resolution.x * closest.resolution.y));

            foreach (var profile in renderScaleProfiles) {
                int profilePixels = profile.resolution.x * profile.resolution.y;
                int diff = Mathf.Abs(targetPixels - profilePixels);

                if (diff < closestDiff) {
                    closest = profile;
                    closestDiff = diff;
                }
            }

            return closest;
        }

        string GetProfileName(Vector2Int resolution) {
            foreach (var profile in renderScaleProfiles) {
                if (profile.resolution == resolution) {
                    return profile.name;
                }
            }

            var closest = GetClosestProfile(resolution);
            return closest != null ? $"~{closest.name}" : "Custom";
        }

        // Public API methods
        public void SetDynamicRenderScale(bool enabled) {
            enableDynamicRenderScale = enabled;
            if (enabled) {
                ApplyGraphicsSettings();
            }
        }

        public void SetManualRenderScale(float scale) {
            if (urpAsset != null) {
                urpAsset.renderScale = Mathf.Clamp01(scale);
                enableDynamicRenderScale = false; // Disable dynamic when manually set
            }
        }

        public float GetCurrentRenderScale() {
            return urpAsset != null ? urpAsset.renderScale : 1.0f;
        }

        public void ForceUpdate() {
            ApplyGraphicsSettings();
        }
    }

    [System.Serializable]
    public class RenderScaleProfile {
        public string name;
        public Vector2Int resolution;
        [Range(0.1f, 1.0f)]
        public float renderScale;
    }
}
