using UnityEngine;

namespace SimpleFPS {
    /// <summary>
    /// Moves an object along a circular path (orbit) instead of rotating it in place.
    /// </summary>
    public class OrbitMover : MonoBehaviour {
        [Header("Orbit Settings")]
        [SerializeField] public Transform center;          // orbit center
        public float radius = 1f;         // orbit radius
        public float speedDeg = 90f;      // degrees per second

        [Header("Orbit Plane")]
        public bool planeXY = false;      // around Z axis
        public bool planeXZ = true;       // around Y axis (default)
        public bool planeYZ = false;      // around X axis

        float angleDeg;                   // current angle

        private void Awake() {
            if (center == null) { center = transform.parent; }  // fallback center
            angleDeg = 0f;
        }

        private void Update() {
            angleDeg += speedDeg * Time.deltaTime;
            float rad = angleDeg * Mathf.Deg2Rad;

            Vector3 offset;
            if (planeXY) {
                offset = new Vector3(Mathf.Cos(rad), Mathf.Sin(rad), 0f) * radius;
            }
            else if (planeYZ) {
                offset = new Vector3(0f, <PERSON><PERSON><PERSON>(rad), Math<PERSON>.<PERSON>(rad)) * radius;
            }
            else { // XZ
                offset = new Vector3(<PERSON><PERSON>.<PERSON><PERSON>(rad), 0f, Math<PERSON>.<PERSON>(rad)) * radius;
            }

            if (center != null) {
                transform.position = center.position + offset;
            }
            else {
                transform.position = offset;
            }
        }
    }
}
