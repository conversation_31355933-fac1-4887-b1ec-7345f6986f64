# Как использовать веревку с физическим взаимодействием игроков

## Быстрый старт

1. **Добавьте компонент Rope** к GameObject в сцене
2. **Настройте точки спавна** веревки в инспекторе
3. **Добавьте следующий код** в ваш скрипт:

```csharp
using UnityEngine;

public class MyRopeSetup : MonoBehaviour {
    void Start() {
        var rope = GetComponent<RopeToolkit.Rope>();
        
        // Включить симуляцию и коллизии
        rope.simulation.enabled = true;
        rope.collisions.enabled = true;
        rope.collisions.influenceRigidbodies = true;
        
        // Включить взаимодействие с игроками
        rope.playerInteraction.enabled = true;
        rope.playerInteraction.forceMultiplier = 2.0f;
        rope.playerInteraction.interactionRadius = 0.5f;
        rope.playerInteraction.checkStride = 2;
    }
}
```

## Настройки взаимодействия с игроками

### Основные параметры:
- **enabled** - включает/выключает взаимодействие
- **forceMultiplier** - сила воздействия на игроков (0.1-10.0)
- **interactionRadius** - радиус обнаружения игроков (0.1-2.0)
- **checkStride** - проверять каждую N-ю частицу (1-10)

### Рекомендуемые значения:
- Для легкого взаимодействия: `forceMultiplier = 1.0f`
- Для сильного взаимодействия: `forceMultiplier = 5.0f`
- Для точного обнаружения: `checkStride = 1`
- Для оптимизации: `checkStride = 3`

## Настройки физики

### Для реалистичной веревки:
```csharp
rope.simulation.stiffness = 0.8f;
rope.simulation.energyLoss = 0.01f;
rope.simulation.gravityMultiplier = 1.0f;
rope.simulation.substeps = 4;
rope.simulation.solverIterations = 3;
```

### Для жесткой веревки:
```csharp
rope.simulation.stiffness = 1.0f;
rope.simulation.substeps = 6;
rope.simulation.solverIterations = 5;
```

### Для мягкой веревки:
```csharp
rope.simulation.stiffness = 0.5f;
rope.simulation.energyLoss = 0.05f;
```

## Оптимизация производительности

1. **Используйте checkStride > 1** для уменьшения количества проверок
2. **Настройте interactionRadius** под размер ваших игроков
3. **Отключайте взаимодействие** для далеких веревок:

```csharp
// Отключить взаимодействие на расстоянии
float distanceToPlayer = Vector3.Distance(transform.position, player.position);
rope.playerInteraction.enabled = distanceToPlayer < 20f;
```

## Динамическое управление

```csharp
// Включить/выключить взаимодействие
rope.playerInteraction.enabled = false;

// Изменить силу в runtime
rope.playerInteraction.forceMultiplier = 3.0f;

// Изменить радиус обнаружения
rope.playerInteraction.interactionRadius = 1.0f;
```

## Отладка

Для визуализации зон взаимодействия добавьте в OnDrawGizmosSelected:

```csharp
void OnDrawGizmosSelected() {
    var rope = GetComponent<RopeToolkit.Rope>();
    if (rope?.playerInteraction.enabled == true && Application.isPlaying) {
        Gizmos.color = Color.yellow;
        for (int i = 0; i < rope.measurements.particleCount; i += rope.playerInteraction.checkStride) {
            Vector3 pos = rope.GetPositionAt(i);
            Gizmos.DrawWireSphere(pos, rope.playerInteraction.interactionRadius);
        }
    }
}
```

## Требования

- Unity 2021.3+
- Fusion 2.0 для сетевого взаимодействия
- PlayerController с методом RPC_ApplyKnockback
- Слои "LocalPlayerBody" и "ClientPlayerBody"

## Примеры использования

- **Качели** - игроки толкают веревку и получают импульс
- **Препятствия** - веревка отталкивает игроков при движении
- **Интерактивные элементы** - веревка реагирует на прикосновения игроков
