# Rope Player Interaction

Эта функциональность добавляет физическое взаимодействие между веревкой и игроками в Unity проекте.

## Возможности

- **Автоматическое обнаружение коллизий** между частицами веревки и игроками
- **Применение физических сил** к игрокам при контакте с веревкой
- **Реакция веревки** на взаимодействие с игроками (закон Ньютона)
- **Настраиваемые параметры** для точной настройки взаимодействия
- **Оптимизированная производительность** с настраиваемым шагом проверки

## Настройка

### 1. Базовая настройка веревки

```csharp
// Включить симуляцию
rope.simulation.enabled = true;

// Включить коллизии
rope.collisions.enabled = true;
rope.collisions.influenceRigidbodies = true;

// Включить взаимодействие с игроками
rope.playerInteraction.enabled = true;
```

### 2. Параметры взаимодействия с игроками

- **enabled**: Включает/выключает взаимодействие с игроками
- **forceMultiplier**: Множитель силы, применяемой к игрокам (0.1-10.0)
- **interactionRadius**: Радиус вокруг частиц веревки для проверки коллизий (0.1-2.0)
- **checkStride**: Проверять каждую N-ю частицу для оптимизации производительности (1-10)

### 3. Рекомендуемые настройки физики

```csharp
rope.simulation.stiffness = 0.8f;           // Жесткость веревки
rope.simulation.energyLoss = 0.01f;         // Потеря энергии
rope.simulation.gravityMultiplier = 1.0f;   // Множитель гравитации
rope.simulation.substeps = 4;               // Подшаги симуляции
rope.simulation.solverIterations = 3;       // Итерации решателя
```

## Использование

### Автоматическая настройка

Добавьте компонент `RopePlayerInteraction` к объекту с веревкой:

```csharp
// Компонент автоматически настроит веревку для взаимодействия с игроками
var ropeInteraction = gameObject.AddComponent<RopePlayerInteraction>();
ropeInteraction.forceMultiplier = 2.0f;
ropeInteraction.interactionRadius = 0.5f;
```

### Ручная настройка

```csharp
var rope = GetComponent<Rope>();

// Настройка взаимодействия с игроками
rope.playerInteraction.enabled = true;
rope.playerInteraction.forceMultiplier = 2.0f;
rope.playerInteraction.interactionRadius = 0.5f;
rope.playerInteraction.checkStride = 2;
```

### Динамическое управление

```csharp
// Включить/выключить взаимодействие
rope.playerInteraction.enabled = false;

// Изменить силу взаимодействия
rope.playerInteraction.forceMultiplier = 5.0f;

// Изменить радиус взаимодействия
rope.playerInteraction.interactionRadius = 1.0f;
```

## Как это работает

1. **Обнаружение коллизий**: Каждый FixedUpdate веревка проверяет коллизии вокруг своих частиц
2. **Расчет силы**: Сила рассчитывается на основе скорости частицы веревки и настроек
3. **Применение силы**: Сила применяется к игроку через систему knockback
4. **Реакция веревки**: Веревка получает обратную силу согласно третьему закону Ньютона

## Оптимизация производительности

- Используйте `checkStride > 1` для проверки не каждой частицы
- Настройте `interactionRadius` под размер ваших игроков
- Отключайте взаимодействие для веревок, которые далеко от игроков

## Требования

- Unity 2021.3 или новее
- Fusion 2.0 для сетевого взаимодействия
- PlayerController с методом RPC_ApplyKnockback
- Слои "LocalPlayerBody" и "ClientPlayerBody" для игроков

## Примечания

- Взаимодействие работает только с живыми игроками (IsAlive = true)
- Силы применяются только на сервере (HasStateAuthority)
- Система оптимизирована для многопользовательских игр
