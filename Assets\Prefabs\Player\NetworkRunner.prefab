%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1781180110049439631
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1276750293019490638}
  - component: {fileID: 3095207314023402545}
  - component: {fileID: 230964205106679574}
  - component: {fileID: 8165141269578965672}
  - component: {fileID: 6808256441503993390}
  - component: {fileID: -5951940427854792807}
  - component: {fileID: 1497326351679446957}
  m_Layer: 0
  m_Name: NetworkRunner
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1276750293019490638
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1781180110049439631}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &3095207314023402545
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1781180110049439631}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -1199893898, guid: e725a070cec140c4caffb81624c8c787, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &230964205106679574
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1781180110049439631}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -232609262, guid: e725a070cec140c4caffb81624c8c787, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  OnInput:
    m_PersistentCalls:
      m_Calls: []
  OnInputMissing:
    m_PersistentCalls:
      m_Calls: []
  OnConnectedToServer:
    m_PersistentCalls:
      m_Calls: []
  OnDisconnectedFromServer:
    m_PersistentCalls:
      m_Calls: []
  OnConnectRequest:
    m_PersistentCalls:
      m_Calls: []
  OnConnectFailed:
    m_PersistentCalls:
      m_Calls: []
  PlayerJoined:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: -5951940427854792807}
        m_TargetAssemblyTypeName: SimpleFPS.NetworkEventsHandler, Dvor.Scripts.Runtime
        m_MethodName: OnPlayerJoined
        m_Mode: 0
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  PlayerLeft:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: -5951940427854792807}
        m_TargetAssemblyTypeName: SimpleFPS.NetworkEventsHandler, Dvor.Scripts.Runtime
        m_MethodName: OnPlayerLeft
        m_Mode: 0
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  OnSimulationMessage:
    m_PersistentCalls:
      m_Calls: []
  OnShutdown:
    m_PersistentCalls:
      m_Calls: []
  OnSessionListUpdate:
    m_PersistentCalls:
      m_Calls: []
  OnCustomAuthenticationResponse:
    m_PersistentCalls:
      m_Calls: []
  OnHostMigration:
    m_PersistentCalls:
      m_Calls: []
  OnSceneLoadDone:
    m_PersistentCalls:
      m_Calls: []
  OnSceneLoadStart:
    m_PersistentCalls:
      m_Calls: []
  OnReliableData:
    m_PersistentCalls:
      m_Calls: []
  OnReliableProgress:
    m_PersistentCalls:
      m_Calls: []
  OnObjectEnterAOI:
    m_PersistentCalls:
      m_Calls: []
  OnObjectExitAOI:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &8165141269578965672
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1781180110049439631}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90a93abf1a391964a94e5c139605105b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  IsSceneTakeOverEnabled: 1
  LogSceneLoadErrors: 0
--- !u!114 &6808256441503993390
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1781180110049439631}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7aee3749b104db88b4339b3346607a7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &-5951940427854792807
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1781180110049439631}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1dbf91b83dd681f4988e30d309ac8177, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  gameManagerPrefab: {fileID: 3622153684620818428, guid: ea970866e1e2db84eaab1e6763a836ee, type: 3}
  gameUIPrefab: {fileID: 627947034833360697, guid: ff5e7b360d1cbd74bb353015bacd5b0c, type: 3}
  playerCameraPrefab: {fileID: 2980101415315923734, guid: ee51f5fc8c390a84482cc22f3f4ca2e3, type: 3}
--- !u!114 &1497326351679446957
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1781180110049439631}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b8edbe7108d8a43ab838cf8b8d9453df, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
