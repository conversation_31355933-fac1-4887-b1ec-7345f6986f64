using UnityEngine;
using RopeToolkit;

/// <summary>
/// Example script showing how to set up a rope with player physics interaction.
/// Attach this to a GameObject with a Rope component to enable player interaction.
/// </summary>
public class RopePlayerInteraction : MonoBehaviour {
    
    [Header("Rope Setup")]
    [Tooltip("The rope component that will interact with players")]
    public Rope rope;
    
    [<PERSON><PERSON>("Interaction Settings")]
    [Tooltip("Enable/disable player interaction at runtime")]
    public bool enableInteraction = true;
    
    [Tooltip("Force multiplier for player interactions")]
    [Range(0.1f, 10.0f)]
    public float forceMultiplier = 2.0f;
    
    [Tooltip("Radius around rope particles to check for players")]
    [Range(0.1f, 2.0f)]
    public float interactionRadius = 0.5f;
    
    [Tooltip("Check every N particles for performance")]
    [Range(1, 10)]
    public int checkStride = 2;
    
    void Start() {
        // Get rope component if not assigned
        if (rope == null) {
            rope = GetComponent<Rope>();
        }
        
        if (rope == null) {
            Debug.LogError("RopePlayerInteraction: No Rope component found!");
            enabled = false;
            return;
        }
        
        // Configure rope for player interaction
        SetupRopeForPlayerInteraction();
    }
    
    void Update() {
        // Update rope settings if they changed
        if (rope != null) {
            rope.playerInteraction.enabled = enableInteraction;
            rope.playerInteraction.forceMultiplier = forceMultiplier;
            rope.playerInteraction.interactionRadius = interactionRadius;
            rope.playerInteraction.checkStride = checkStride;
        }
    }
    
    /// <summary>
    /// Configures the rope component for optimal player interaction
    /// </summary>
    void SetupRopeForPlayerInteraction() {
        // Enable simulation
        rope.simulation.enabled = true;
        
        // Enable collisions for better physics
        rope.collisions.enabled = true;
        rope.collisions.influenceRigidbodies = true;
        
        // Configure player interaction settings
        rope.playerInteraction.enabled = enableInteraction;
        rope.playerInteraction.forceMultiplier = forceMultiplier;
        rope.playerInteraction.interactionRadius = interactionRadius;
        rope.playerInteraction.checkStride = checkStride;
        
        // Recommended physics settings for player interaction
        rope.simulation.stiffness = 0.8f;
        rope.simulation.energyLoss = 0.01f;
        rope.simulation.gravityMultiplier = 1.0f;
        rope.simulation.substeps = 4;
        rope.simulation.solverIterations = 3;
        
        Debug.Log($"Rope '{rope.name}' configured for player interaction");
    }
    
    /// <summary>
    /// Enable or disable player interaction at runtime
    /// </summary>
    public void SetPlayerInteractionEnabled(bool enabled) {
        enableInteraction = enabled;
        if (rope != null) {
            rope.playerInteraction.enabled = enabled;
        }
    }
    
    /// <summary>
    /// Adjust the force multiplier at runtime
    /// </summary>
    public void SetForceMultiplier(float multiplier) {
        forceMultiplier = Mathf.Clamp(multiplier, 0.1f, 10.0f);
        if (rope != null) {
            rope.playerInteraction.forceMultiplier = forceMultiplier;
        }
    }
    
    /// <summary>
    /// Adjust the interaction radius at runtime
    /// </summary>
    public void SetInteractionRadius(float radius) {
        interactionRadius = Mathf.Clamp(radius, 0.1f, 2.0f);
        if (rope != null) {
            rope.playerInteraction.interactionRadius = interactionRadius;
        }
    }
    
#if UNITY_EDITOR
    void OnDrawGizmosSelected() {
        if (rope == null || !rope.playerInteraction.enabled) return;
        
        // Draw interaction spheres around rope particles
        if (Application.isPlaying && rope.measurements.particleCount > 0) {
            Gizmos.color = Color.yellow;
            for (int i = 0; i < rope.measurements.particleCount; i += rope.playerInteraction.checkStride) {
                Vector3 particlePos = rope.GetPositionAt(i);
                Gizmos.DrawWireSphere(particlePos, rope.playerInteraction.interactionRadius);
            }
        }
    }
#endif
}
