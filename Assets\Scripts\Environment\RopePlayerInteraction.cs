using UnityEngine;

/// <summary>
/// Example script showing how to set up a rope with player physics interaction.
/// This script demonstrates the usage of the new player interaction features.
///
/// IMPORTANT: This script is for demonstration purposes only.
/// To actually use the rope player interaction, you need to:
/// 1. Add a Rope component from RopeToolkit to your GameObject
/// 2. Configure the rope.playerInteraction settings directly in code or inspector
/// 3. Enable rope.simulation and rope.collisions
///
/// Example usage in your own scripts:
///
/// var rope = GetComponent<RopeToolkit.Rope>();
/// rope.playerInteraction.enabled = true;
/// rope.playerInteraction.forceMultiplier = 2.0f;
/// rope.playerInteraction.interactionRadius = 0.5f;
/// rope.playerInteraction.checkStride = 2;
///
/// rope.simulation.enabled = true;
/// rope.collisions.enabled = true;
/// rope.collisions.influenceRigidbodies = true;
/// </summary>
public class RopePlayerInteraction : MonoBehaviour {

    [Header("Demonstration Settings")]
    [Tooltip("Enable/disable player interaction")]
    public bool enableInteraction = true;

    [Tooltip("Force multiplier for player interactions")]
    [Range(0.1f, 10.0f)]
    public float forceMultiplier = 2.0f;

    [Tooltip("Radius around rope particles to check for players")]
    [Range(0.1f, 2.0f)]
    public float interactionRadius = 0.5f;

    [Tooltip("Check every N particles for performance")]
    [Range(1, 10)]
    public int checkStride = 2;

    void Start() {
        Debug.Log("RopePlayerInteraction: This is a demonstration script.");
        Debug.Log("To use rope player interaction, configure the Rope component directly:");
        Debug.Log("rope.playerInteraction.enabled = true;");
        Debug.Log("rope.playerInteraction.forceMultiplier = " + forceMultiplier + "f;");
        Debug.Log("rope.playerInteraction.interactionRadius = " + interactionRadius + "f;");
        Debug.Log("rope.playerInteraction.checkStride = " + checkStride + ";");

        Debug.Log("\nExample setup code:");
        Debug.Log("rope.simulation.enabled = true;");
        Debug.Log("rope.collisions.enabled = true;");
        Debug.Log("rope.collisions.influenceRigidbodies = true;");
    }
}
